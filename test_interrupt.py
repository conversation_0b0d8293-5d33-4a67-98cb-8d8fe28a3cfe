#!/usr/bin/env python3
"""
Test script to verify that Ctrl+C interruption works correctly.
This script simulates a long-running process that can be interrupted.
"""

import time
import signal
import sys
import logging

# Global flag to track if we should stop
should_stop = False

def signal_handler(signum, frame):
    """Handle Ctrl+C signal."""
    global should_stop
    print("\nReceived Ctrl+C signal. Stopping...")
    should_stop = True

def main():
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    print("Starting long-running test process...")
    print("Press Ctrl+C to interrupt")
    
    counter = 0
    while not should_stop and counter < 100:
        print(f"Working... {counter}/100")
        time.sleep(1)
        counter += 1
    
    if should_stop:
        print("Process was interrupted by user")
        sys.exit(1)
    else:
        print("Process completed normally")
        sys.exit(0)

if __name__ == "__main__":
    main()
